import { createClient } from "@/lib/server";
import { SupabaseClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const supabase = await createClient();

    const email = formData.get("email") as string;
    const password = formData.get("password") as string;

    // Validate required fields
    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 }
      );
    }

    // Check if user already exists in the users table
    const { data: existingUser, error: getUserError } = await supabase
      .from("users")
      .select("id")
      .eq("email", email)
      .maybeSingle();

    if (getUserError) {
      console.error("Error checking existing user:", getUserError);
      return NextResponse.json(
        { error: "Failed to verify user status" },
        { status: 500 }
      );
    }

    if (existingUser) {
      return NextResponse.json(
        {
          error: "Account already exists with this email. Please try to login.",
        },
        { status: 409 }
      );
    }

    const {
      data: { user },
      error,
    } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/confirm?next=/dashboard`,
      },
    });

    if (error || !user) {
      // Check if the error is due to user already existing
      if (
        error?.message?.includes("already registered") ||
        error?.message?.includes("already exists") ||
        error?.message?.includes("User already registered")
      ) {
        return NextResponse.json(
          {
            error:
              "Account already exists with this email. Please try to login.",
          },
          { status: 409 }
        );
      }

      return NextResponse.json(
        { error: error?.message || "Signup failed" },
        { status: 400 }
      );
    }

    const planId = await getFreePlanId(supabase);

    if (!planId) {
      await supabase.auth.admin.deleteUser(user.id);
      return NextResponse.json(
        { error: "Failed to assign plan" },
        { status: 500 }
      );
    }

    const { error: insertError } = await supabase.from("users").insert({
      id: user.id,
      email: email,
      plan_id: planId,
      provider: "email",
    });

    if (insertError) {
      await supabase.auth.admin.deleteUser(user.id);
      return NextResponse.json(
        { error: "Failed to create user profile" },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        success: true,
        message: "Signup successful",
        redirectTo: "/auth/sign-up-success",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Signup error:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}

async function getFreePlanId(supabase: SupabaseClient) {
  const { data } = await supabase
    .from("plans")
    .select("id")
    .eq("name", "Free")
    .single();
  return data?.id;
}
