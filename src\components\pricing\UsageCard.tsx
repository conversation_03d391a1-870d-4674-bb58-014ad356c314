import React from "react";

type UsageCardProps = React.FC<{
  title: string;
  total: number;
  used: number;
}>;
const UsageCard: UsageCardProps = ({ title, total, used }) => {
  const percentage = Math.round((+used / +total) * 100);
  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="text-sm font-bold mb-2">{title}</h3>
        <div className="relative w-16 h-16 mx-auto">
          <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
            <path
              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
              fill="none"
              stroke="#e5e7eb"
              strokeWidth="2"
            />
            <path
              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
              fill="none"
              stroke="#10b981"
              strokeWidth="2"
              strokeDasharray={`${percentage}, 100`}
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-sm font-semibold text-[#2D2D2D]">
              {percentage}%
            </span>
          </div>
        </div>
        <p className="text-sm mt-1 font-semibold text-[#2D2D2D]">
          {used}/{total}
        </p>
      </div>
    </div>
  );
};

export default UsageCard;
