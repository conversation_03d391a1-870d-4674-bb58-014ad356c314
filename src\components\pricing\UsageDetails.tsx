import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "../ui/card";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import PlanFeatures from "./PlanFeatures";
import UsageCard from "./UsageCard";
import { UsersType } from "@/types/db";
import { Maybe } from "@/types/common";

type UsageDetailsProps = React.FC<{
  userData: Maybe<UsersType>;
}>;
const UsageDetails: UsageDetailsProps = ({ userData }) => {
  const planName = userData?.plan?.name;

  return (
    <Card className="glassColor w-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between gap-2 flex-wrap">
          <div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-black">Your Current Plan :</span>
              <span className="font-semibold">{planName}</span>
              <Badge
                variant="secondary"
                className="bg-white rounded-full text-[#16A34A]"
              >
                Active
              </Badge>
            </div>
            <p className="text-xs text-[#4F4F4F]">
              Renews on July 1 2025 ,
              <span className="text-[#4F4F4F] font-semibold">
                €0/ per month
              </span>
            </p>
          </div>
          <div className="gap-2 hidden md:flex">
            <Button
              variant="outline"
              className="bg-white cursor-pointer text-red-600 hover:text-red-700"
            >
              Cancel Subscription
            </Button>
            <Button className="bg-[#028482] py-0 cursor-pointer hover:bg-[#028482]/90">
              Upgrade Plan
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex flex-row flex-wrap">
        <div className="flex flex-col lg:flex-row gap-6 border-none px-6 w-full lg:w-1/2 lg: border-r">
          <UsageCard title="Credits per month" total={2000} used={590} />
          <UsageCard title="Daily Limit" total={4000} used={2000} />
          <UsageCard title="Auto Enrichment limit" total={100} used={20} />
        </div>
        <div className="w-full lg:w-1/2">
          <PlanFeatures userData={userData} />
        </div>
      </CardContent>
      <div className="px-4 flex flex-col gap-3 md:hidden">
        <Button
          variant="outline"
          className="bg-white cursor-pointer w-full text-red-600 hover:text-red-700"
        >
          Cancel Subscription
        </Button>
        <Button className="bg-[#028482] py-0 w-full cursor-pointer hover:bg-[#028482]/90">
          Upgrade Plan
        </Button>
      </div>
    </Card>
  );
};

export default UsageDetails;
