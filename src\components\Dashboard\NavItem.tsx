"use client";

import DashboardIcon from "@/assets/DashboardIcon";
import { dashboardNavSingleType } from "@/types/dashboard";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";

type NavItemProps = React.FC<{
  data: dashboardNavSingleType;
  onClick?: () => void;
  isSidebar?: boolean;
}>;
const NavItem: NavItemProps = ({ data, onClick, isSidebar = false }) => {
  const pathname = usePathname();
  const fullHref = `/dashboard${data.href}`;

  // Check if current route matches this nav item
  // For the root dashboard ("/dashboard/"), match exactly
  // For other routes, check if pathname starts with the href
  const isActive =
    data.href === "/"
      ? pathname === "/dashboard" || pathname === "/dashboard/"
      : pathname.startsWith(fullHref);

  return (
    <Link
      href={fullHref}
      onClick={onClick}
      key={data.id}
      className={`flex items-center gap-3 px-3 py-2 rounded-lg transition-colors ${
        isActive
          ? isSidebar
            ? "bg-[#D2FFEC]"
            : "bg-[#c8e7ea] outline-[#e8f5f7]"
          : "text-gray-800 hover:outline hover:bg-[#c8e7ea] hover:outline-[#e8f5f7]"
      }`}
    >
      <DashboardIcon type={data.id} />
      <span className={`font-bold text-[#005857]`}>{data.name}</span>
    </Link>
  );
};

export default NavItem;
