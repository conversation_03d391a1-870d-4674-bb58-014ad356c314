import { dashboardNavSingleType } from "@/types/dashboard";

enum DashboardIconType {
  MY_SPACE = 1,
  LEAD_GENERATION_TOOL = 2,
  PRICING = 3,
  SUPPORT = 4,
  LOGOUT = 5,
}

const dashboardNav: dashboardNavSingleType[] = [
  {
    id: DashboardIconType.MY_SPACE,
    name: "My Space",
    href: "/",
  },
  {
    id: DashboardIconType.LEAD_GENERATION_TOOL,
    name: "Lead Generation Tool",
    href: "/tool",
  },
  {
    id: DashboardIconType.PRICING,
    name: "Pricing",
    href: "/pricing",
  },
  {
    id: DashboardIconType.SUPPORT,
    name: "Support",
    href: "/support",
  },
];

export { dashboardNav, DashboardIconType };
