"use client";

import { createClient } from "@/lib/client";
import { useRouter } from "next/navigation";

import React from "react";
import NavItem from "./NavItem";
import { DashboardIconType } from "@/constant/dashboard";

const Logout = () => {
  const router = useRouter();

  const logout = async () => {
    const supabase = createClient();
    await supabase.auth.signOut();
    router.push("/auth/login");
  };

  return (
    <NavItem
      onClick={logout}
      data={{
        id: DashboardIconType.LOGOUT,
        name: "Logout",
        href: "/#",
      }}
    />
  );
};

export default Logout;
