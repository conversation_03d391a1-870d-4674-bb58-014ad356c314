/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { useRef, useState } from "react";
import { z } from "zod";
import { GoogleAuthButton } from "./google-auth-button";
import AuthSeparator from "./AuthSeparator";

const schema = z
  .object({
    email: z.string().email({ message: "Please enter a valid email address." }),
    password: z
      .string()
      .min(6, { message: "Password must be at least 6 characters long." }),
    "repeat-password": z.string().min(6, {
      message: "Repeat password must be at least 6 characters long.",
    }),
  })
  .refine((data) => data.password === data["repeat-password"], {
    path: ["repeat-password"],
    message: "Passwords do not match.",
  });

export function SignUpForm() {
  const formRef = useRef<HTMLFormElement>(null);
  const [repeatPassword, setRepeatPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formRef.current) return;

    const formData = new FormData(formRef.current);
    const password = formData.get("password") as string;

    const result = schema.safeParse({
      email: formData.get("email") as string,
      password,
      "repeat-password": repeatPassword,
    });

    if (!result.success) {
      const errorMessages = result.error.errors
        .map((err: any) => err.message)
        .join(" ");
      setError(errorMessages);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/auth/sign-up", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        router.push("/auth/sign-up-success");
        return;
      }

      const responseData = await response.json();

      if (!response.ok) {
        setError(responseData.error || "Signup failed");
        return;
      }

      if (responseData.redirectTo) {
        router.push(responseData.redirectTo);
      }
    } catch (err) {
      console.error("Signup error:", err);
      setError("Something went wrong. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            name="email"
            required
            className="w-full"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <Input
            id="password"
            type="password"
            name="password"
            required
            className="w-full"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="repeat-password">Repeat Password</Label>
          <Input
            id="repeat-password"
            type="password"
            required
            value={repeatPassword}
            onChange={(e) => setRepeatPassword(e.target.value)}
            className="w-full"
          />
        </div>
      </div>

      {error && (
        <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
          {error}
        </div>
      )}

      <Button
        type="submit"
        className="w-full text-white border-0 cursor-pointer primaryButton"
        disabled={isLoading}
      >
        {isLoading ? "Signing up..." : "Sign up"}
      </Button>

      <div className="text-center text-sm text-gray-600">
        Already have an account?{" "}
        <Link
          href="/auth/login"
          className="text-blue-600 hover:text-blue-500 font-medium"
        >
          Login
        </Link>
      </div>

      <AuthSeparator />

      <GoogleAuthButton isSignup />
    </form>
  );
}
