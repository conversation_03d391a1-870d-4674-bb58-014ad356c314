"use client";

import React from "react";
import { Card } from "@/components/ui/card";
import { useUser } from "@/components/AppProvider";

const Myspace = () => {
  const userData = useUser();

  if (!userData) {
    return null;
  }

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h1 className="text-2xl font-bold mb-4">Welcome to My Space</h1>
        <div className="space-y-2">
          <p className="text-gray-600">
            <span className="font-medium">Email:</span> {userData.email}
          </p>
          <p className="text-gray-600">
            <span className="font-medium">Current Plan:</span>{" "}
            {userData.plan?.name}
          </p>
        </div>
      </Card>
    </div>
  );
};

export default Myspace;
