/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { createClient } from "@/lib/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

import { z } from "zod";

const schema = z.object({
  password: z
    .string()
    .min(6, { message: "Password must be at least 6 characters" }),
});

export function UpdatePasswordForm() {
  const [password, setPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const exchangeSession = async () => {
      const supabase = createClient();
      const code = new URLSearchParams(window.location.search).get("code");

      if (code) {
        const { error } = await supabase.auth.exchangeCodeForSession(code);
        console.log("error", error);
        if (error) {
          // setError("Session expired or invalid.");
          return;
        }
      }
    };

    exchangeSession();
  }, []);

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!password) {
      setError("Please enter a new password");
      return;
    }

    const result = schema.safeParse({
      password,
    });

    if (!result.success) {
      const errorMessages = result.error.errors
        .map((err: any) => err.message)
        .join(" ");
      setError(errorMessages);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("password", password);

      const response = await fetch("/api/auth/update-password", {
        method: "POST",
        body: formData,
      });

      const responseData = await response.json();

      if (!response.ok) {
        setError(responseData.error || "Failed to update password");
        return;
      }

      // Redirect on success
      if (responseData.redirectTo) {
        router.push(responseData.redirectTo);
      }
    } catch (err) {
      console.error("Reset password failed:", err);
      setError("Something went wrong. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleResetPassword} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="password">New password</Label>
        <Input
          id="password"
          name="password"
          type="password"
          placeholder="New password"
          required
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="w-full"
        />
      </div>

      {error && (
        <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
          {error}
        </div>
      )}

      <Button
        type="submit"
        className="w-full text-white border-0"
        disabled={isLoading}
        style={{
          background:
            "linear-gradient(107.94deg, rgba(3, 243, 144, 0.8) -6.38%, rgba(2, 132, 130, 0.8) 111.04%)",
        }}
      >
        {isLoading ? "Saving..." : "Save new password"}
      </Button>
    </form>
  );
}
