import LayoutContainer from "@/components/LayoutContainer";
import Link from "next/link";
import Logo from "@/components/Logo";

export default function Home() {
  return (
    <LayoutContainer>
      <main className="min-h-screen p-6 flex flex-col items-center justify-center relative">
        {/* Centered Floating Circular Navbar */}
        <div className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50">
          <div className="glassColor w-full rounded-full px-5 py-3 flex items-center gap-6 shadow-lg">
            <Logo size={32} />
            <div className="flex items-center gap-6">
              <Link
                href="/#"
                className="text-sm font-medium hover:opacity-80 transition-opacity"
              >
                Home
              </Link>
              <Link
                href="/#"
                className="text-sm font-medium hover:opacity-80 transition-opacity"
              >
                Features
              </Link>
              <Link
                href="/#"
                className="text-sm font-medium hover:opacity-80 transition-opacity"
              >
                About
              </Link>
              <Link
                href="/auth/sign-up"
                className="primaryButton px-4 py-2 rounded-full text-sm font-medium"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>

        {/* Hero Section */}
        <div className="max-w-3xl text-center space-y-8 mt-16">
          <h1 className="text-4xl md:text-5xl font-bold text-white">
            Transform Your Business with Pointify
          </h1>
          <p className="text-xl text-white/90">
            The all-in-one platform that supercharges your lead generation, 
            streamlines customer acquisition, and accelerates business growth with 
            powerful AI-driven tools and actionable insights
          </p>
          <Link
            href="/auth/sign-up"
            className="primaryButton rounded-full inline-block px-8 py-3 text-lg font-bold shadow-lg hover:opacity-90 transition-opacity"
          >
            Get Started
          </Link>
        </div>
      </main>
    </LayoutContainer>
  );
}
