import { createClient } from "@/lib/server";
import { type EmailOtpType } from "@supabase/supabase-js";
import { NextResponse } from "next/server";
import { type NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const token_hash = searchParams.get("token_hash");
  const type = searchParams.get("type") as EmailOtpType | null;
  const code = searchParams.get("code");
  const _next = searchParams.get("next");
  const next = _next?.startsWith("/") ? _next : "/";

  console.log("Email confirmation attempt:", {
    token_hash: token_hash ? "Present" : "Missing",
    type,
    code: code ? "Present" : "Missing",
    next,
    url: request.url,
  });

  const supabase = await createClient();

  // Handle new auth flow with code parameter
  if (code) {
    const { error } = await supabase.auth.exchangeCodeForSession(code);

    console.log("Code exchange result:", {
      success: !error,
      error: error?.message || "No error",
    });

    if (!error) {
      // Check if this is an OAuth user by getting the user and checking their providers
      const {
        data: { user },
      } = await supabase.auth.getUser();

      console.log("User after code exchange:", {
        userId: user?.id,
        email: user?.email,
        providers: user?.app_metadata?.providers,
      });

      if (user) {
        // Check if user signed in with OAuth (Google)
        const isOAuthUser = user.app_metadata?.providers?.includes("google");

        if (isOAuthUser) {
          // Check if user already exists in our users table
          const { data: existingUser, error: getUserError } = await supabase
            .from("users")
            .select("id, email")
            .eq("id", user.id)
            .maybeSingle();

          if (getUserError) {
            console.error("Error checking existing user:", getUserError);
            return NextResponse.redirect(
              new URL("/auth/error?error=Database error", request.url)
            );
          }

          if (existingUser) {
            // User already exists, just redirect to dashboard
            console.log("Existing OAuth user, redirecting to dashboard");
            return NextResponse.redirect(new URL(next, request.url));
          } else {
            // New OAuth user, redirect to callback handler to create user record
            console.log(
              "New OAuth user detected, redirecting to callback handler"
            );
            return NextResponse.redirect(
              new URL(
                `/api/auth/oauth-callback?next=${encodeURIComponent(next)}`,
                request.url
              )
            );
          }
        }
      }

      console.log("Non-OAuth user, redirecting to:", next);
      return NextResponse.redirect(new URL(next, request.url));
    } else {
      return NextResponse.redirect(
        new URL(
          `/auth/error?error=${encodeURIComponent(
            error?.message || "Code exchange failed"
          )}`,
          request.url
        )
      );
    }
  }

  // Handle legacy auth flow with token_hash and type
  if (token_hash && type) {
    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    });

    console.log("OTP verification result:", {
      success: !error,
      error: error?.message || "No error",
    });

    if (!error) {
      console.log("Redirecting to:", next);
      return NextResponse.redirect(new URL(next, request.url));
    } else {
      return NextResponse.redirect(
        new URL(
          `/auth/error?error=${encodeURIComponent(
            error?.message || "Verification failed"
          )}`,
          request.url
        )
      );
    }
  }

  // redirect the user to an error page with some instructions
  return NextResponse.redirect(
    new URL("/auth/error?error=No%20token%20hash%20or%20type", request.url)
  );
}
