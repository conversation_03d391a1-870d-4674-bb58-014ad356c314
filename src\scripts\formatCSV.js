const fs = require("fs");
const path = require("path");
const { stringify } = require("csv-stringify");
const { parse } = require("csv-parse");

try {
  const inputFile = path.join(__dirname, "input.csv");
  const outputFile = path.join(__dirname, "output.csv");

  const mapFieldToPlatform = {
    facebook_domain: "fb",
    instagram_domain: "ig",
    youtube_domain: "youtube",
    tiktok_domain: "tiktok",
    twitter_domain: "x",
    linkedin_domain: "linkedin",
    INSTAGRAM: "ig",
    YOUTUBE: "youtube",
    TIKTOK: "tiktok",
  };

  const socialFields = Object.keys(mapFieldToPlatform);

  const parseFollowerCount = (val) => {
    if (!val) return 0;
    const str = val.toString().trim();

    if (/^\d+(\.\d+)?[Mm]$/.test(str)) {
      return Math.round(parseFloat(str) * 1_000_000);
    }
    if (/^\d+(\.\d+)?[Kk]$/.test(str)) {
      return Math.round(parseFloat(str) * 1_000);
    }
    const num = parseInt(str.replace(/,/g, ""), 10);
    return isNaN(num) ? 0 : num;
  };

  const rows = [];

  fs.createReadStream(inputFile)
    .pipe(parse({ columns: true }))
    .on("data", (row) => {
      const social = {};

      Object.values(mapFieldToPlatform).forEach((platform) => {
        social[platform] = {
          url: "",
          followers_count: 0,
        };
      });

      socialFields.forEach((field) => {
        const platform = mapFieldToPlatform[field];

        const value = row[field] || "";
        const countField = `${field}_followers`;
        const followersRaw = row[countField] || "0";

        if (value) {
          social[platform].url = value;
        }

        const parsedCount = parseFollowerCount(followersRaw);
        if (parsedCount > 0) {
          social[platform].followers_count = parsedCount;
        }
      });

      rows.push({
        domain: row.domain,
        email: row.email,
        phone: row.phone_numbers,
        categories: row.categorie,
        social: JSON.stringify(social),
        source: row.source,
      });
    })
    .on("end", () => {
      const addHeader = !fs.existsSync(outputFile);

      stringify(
        rows,
        {
          header: addHeader,
          columns: [
            "domain",
            "email",
            "phone",
            "social",
            "categories",
            "source",
          ],
        },
        (err, output) => {
          if (err) throw err;
          fs.appendFileSync(outputFile, output);
          console.log("CSV file successfully processed and saved to", outputFile);
        }
      );
    })
    .on("error", (err) => {
      console.error("Error processing CSV:", err);
    });
} catch (error) {
  console.error("Something went wrong:", error);
}
