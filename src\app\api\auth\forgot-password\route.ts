import { createClient } from "@/lib/server";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const supabase = await createClient();

    const email = formData.get("email") as string;

    // Validate required fields
    if (!email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    // Check if user exists in our users table and what provider they used
    const { data: existingUser, error: getUserError } = await supabase
      .from("users")
      .select("id, email, provider")
      .eq("email", email)
      .maybeSingle();

    if (getUserError) {
      console.error("Error checking existing user:", getUserError);
      return NextResponse.json(
        { error: "Failed to verify user status" },
        { status: 500 }
      );
    }

    // If user exists and is a Google OAuth user, show appropriate message
    if (existingUser && existingUser.provider === "google") {
      return NextResponse.json(
        {
          error:
            "This email is associated with a Google account. Please sign in with Google instead.",
          isOAuthUser: true,
        },
        { status: 400 }
      );
    }

    const redirectUrl =
      process.env.NEXT_PUBLIC_SITE_URL + "/auth/update-password";

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: redirectUrl,
    });

    console.log({
      error,
      email,
      redirectUrl,
    });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    return NextResponse.json(
      {
        success: true,
        message: "Password reset email sent",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Forgot password error:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
