"use client";

import React from "react";
import { dashboardNav } from "@/constant/dashboard";
import NavItem from "./NavItem";
import Logout from "./Logout";
import Logo from "../Logo";

interface SidebarModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SidebarModal: React.FC<SidebarModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <>
      <div
        className="fixed inset-0 bg-transparent blur-xl bg-opacity-50 z-40 md:hidden"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="fixed inset-y-0 left-0 w-full p-4 z-50 md:hidden">
        <div
          style={{
            background: "rgba(255, 255, 255, 0.8)",
          }}
          className="h-full glassColor flex flex-col rounded-lg"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 ">
            <Logo />
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <Close />
            </button>
          </div>

          {/* Navigation */}
          <div className="flex-1 p-4">
            <nav className="space-y-2">
              {dashboardNav.map((item, index) => (
                <div key={index} onClick={onClose}>
                  <NavItem isSidebar data={item} />
                </div>
              ))}
            </nav>
          </div>

          {/* Logout */}
          <div className="p-4 ">
            <div onClick={onClose}>
              <Logout />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

const Close = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18 6L6 18M6 6L18 18"
        stroke="#005857"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default SidebarModal;
