/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { Card } from "@/components/ui/card";
import { PricingGrid } from "@/components/pricing";
import React from "react";
import CreditPolicy from "@/components/pricing/CreditPolicy";
import UsageDetails from "@/components/pricing/UsageDetails";
import { useUser } from "@/components/AppProvider";

const Pricing = () => {
  const userData = useUser();
  return (
    <div className="flex-1 h-full Support">
      <Card className="h-full glassColor relative p-6">
        <UsageDetails userData={userData} />

        <PricingGrid
          activePlanName={userData?.plan?.name ?? ""}
          onUpgrade={(planId) => {
            console.log("Upgrading to plan:", planId);
          }}
        />

        <CreditPolicy />
      </Card>
    </div>
  );
};

export default Pricing;
