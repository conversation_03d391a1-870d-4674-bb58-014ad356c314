"use client";

import { pricingPlans } from "@/data/pricing-plans";
import { PricingCard } from "./PricingCard";

interface PricingGridProps {
  onUpgrade?: (planId: string) => void;
  showCreditPolicy?: boolean;
  activePlanName: string;
}

export const PricingGrid = ({
  onUpgrade,
  activePlanName,
}: PricingGridProps) => {
  return (
    <div className="space-y-6">
      {/* Explore Other Plans */}
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold">Explore Other plans</h2>
        <p className="font-semibold text-[0.85rem] mt-2">
          Start your journey with the perfect plan for your business needs
        </p>
      </div>

      <div className="flex flex-row flex-wrap">
        {pricingPlans.map((plan) => (
          <PricingCard
            isCurrentPlan={plan.name === activePlanName}
            key={plan.id}
            plan={plan}
            onUpgrade={onUpgrade}
          />
        ))}
      </div>
    </div>
  );
};
