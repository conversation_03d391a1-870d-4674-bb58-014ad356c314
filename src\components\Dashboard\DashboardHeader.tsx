"use client";

import React, { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card } from "../ui/card";
import { Button } from "../ui/button";
import SidebarToggle from "./SidebarToggle";
import SidebarModal from "./SidebarModal";
import Logo from "../Logo";
import { useUser } from "../AppProvider";

const DashboardHeader = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const userData = useUser();

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  const getInitials = (email: string) => {
    return email.charAt(0).toUpperCase();
  };

  return (
    <>
      <div className="w-full flex items-center justify-between">
        <div className="flex flex-row items-center gap-2">
          <SidebarToggle onClick={toggleSidebar} />
          <Logo />
        </div>

        <div className="flex items-center gap-3">
          <PlanInfo />
          <Avatar className="w-10 h-10">
            <AvatarImage src="/placeholder.svg" />
            <AvatarFallback>
              {userData?.email ? getInitials(userData.email) : "U"}
            </AvatarFallback>
          </Avatar>
        </div>
      </div>

      <SidebarModal isOpen={isSidebarOpen} onClose={closeSidebar} />
    </>
  );
};

const PlanInfo = () => {
  const userData = useUser();
  const planName = userData?.plan?.name || "Free";
  const isFreePlan = planName.toLowerCase() === "free";

  return (
    <Card className="flex flex-row p-1 gap-3 rounded-full glassColor text-center items-center">
      <span className="text-sm text-[rgba(57, 57, 57, 1)]">
        Plan: {planName}
      </span>
      {isFreePlan && (
        <Button
          size="sm"
          className="primaryButton rounded-full px-4 py-2 h-auto text-xs"
        >
          Upgrade Plan
        </Button>
      )}
    </Card>
  );
};

export default DashboardHeader;
