"use client";

import React from "react";

interface SidebarToggleProps {
  onClick: () => void;
}

const SidebarToggle: React.FC<SidebarToggleProps> = ({ onClick }) => {
  return (
    <button
      onClick={onClick}
      className="md:hidden p-2 hover:bg-white/10 rounded-lg transition-colors"
    >
      <svg
        width="43"
        height="43"
        viewBox="0 0 43 43"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9 32H34M9 21.5H34M9 11H34"
          stroke="white"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </button>
  );
};

export default SidebarToggle;
