"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import React, { useEffect, useRef, useState } from "react";
import { z } from "zod";
import { toast } from "sonner";
import { useUser } from "@/components/AppProvider";

const supportSchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  subject: z.string().min(1, { message: "Subject is required" }),
  message: z
    .string()
    .min(10, { message: "Message must be at least 10 characters long" }),
});

const Support = () => {
  const formRef = useRef<HTMLFormElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [userEmail, setUserEmail] = useState<string>("");
  const [userName, setUserName] = useState<string>("");
  const [fieldErrors, setFieldErrors] = useState<{
    name?: string;
    subject?: string;
    message?: string;
  }>({});

  const userData = useUser();

  useEffect(() => {
    if (userData) {
      setUserEmail(userData.email || "");
      const emailName = userData.email?.split("@")[0] || "";
      setUserName(emailName);
    }
  }, [userData]);

  const validateField = (name: string, value: string) => {
    const newErrors = { ...fieldErrors };

    switch (name) {
      case "name":
        if (!value.trim()) {
          newErrors.name = "Name is required";
        } else {
          delete newErrors.name;
        }
        break;
      case "subject":
        if (!value.trim()) {
          newErrors.subject = "Subject is required";
        } else {
          delete newErrors.subject;
        }
        break;
      case "message":
        if (!value.trim()) {
          newErrors.message = "Message is required";
        } else if (value.trim().length < 10) {
          newErrors.message = "Message must be at least 10 characters long";
        } else {
          delete newErrors.message;
        }
        break;
    }

    setFieldErrors(newErrors);
  };

  const handleFieldChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    validateField(name, value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formRef.current) return;

    setIsLoading(true);

    const formData = new FormData(formRef.current);
    const data = {
      name: formData.get("name") as string,
      email: formData.get("email") as string,
      subject: formData.get("subject") as string,
      message: formData.get("message") as string,
    };

    // Validate all fields before submission
    validateField("name", data.name);
    validateField("subject", data.subject);
    validateField("message", data.message);

    // Frontend validation using Zod
    const result = supportSchema.safeParse(data);

    if (!result.success) {
      const errorMessages = result.error.errors
        .map((err) => err.message)
        .join(", ");
      toast.error(errorMessages);
      setIsLoading(false);
      return;
    }

    // Check if there are any field errors
    if (Object.keys(fieldErrors).length > 0) {
      toast.error("Please fix the errors in the form before submitting");
      setIsLoading(false);
      return;
    }

    // Additional frontend validations
    if (data.name.trim().length === 0) {
      toast.error("Name cannot be empty");
      setIsLoading(false);
      return;
    }

    if (data.subject.trim().length === 0) {
      toast.error("Subject cannot be empty");
      setIsLoading(false);
      return;
    }

    if (data.message.trim().length < 10) {
      toast.error("Message must be at least 10 characters long");
      setIsLoading(false);
      return;
    }

    if (data.email !== userEmail) {
      toast.error("Email field has been tampered with");
      setIsLoading(false);
      return;
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      toast.error("Please enter a valid email address");
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/support", {
        method: "POST",
        body: formData,
      });

      const responseData = await response.json();

      if (!response.ok) {
        toast.error(responseData.error || "Failed to submit support request");
        return;
      }

      toast.success(
        "Your support request has been submitted successfully. We'll get back to you soon!"
      );
      formRef.current.reset();
      setFieldErrors({});
      setUserName(userName);
    } catch (error) {
      console.error("Support submission error:", error);
      toast.error("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex-1 h-full Support">
      <Card className="h-full glassColor relative">
        {/* Contact Support Form */}
        <div className="flex items-start justify-center h-full px-8">
          <div className="w-full max-w-md space-y-6">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Contact Support
              </h1>
              <p className="text-gray-900 text-sm font-bold">
                Have a question or need assistance? Please fill out the form
                below or reach out to us directly via email.
              </p>
            </div>

            <form ref={formRef} onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label>Name</Label>
                <Input
                  name="name"
                  defaultValue={userName}
                  placeholder="Enter your name"
                  className={`bg-white/50 border-white/30 backdrop-blur-sm ${
                    fieldErrors.name ? "border-red-500" : ""
                  }`}
                  onChange={handleFieldChange}
                  required
                />
                {fieldErrors.name && (
                  <p className="text-red-500 text-xs mt-1">
                    {fieldErrors.name}
                  </p>
                )}
              </div>

              <div>
                <Label>Email Id</Label>
                <Input
                  name="email"
                  type="email"
                  value={userEmail}
                  readOnly
                  className="bg-gray-100/50 border-white/30 backdrop-blur-sm cursor-not-allowed"
                  title="Email is automatically filled from your account"
                />
              </div>

              <div>
                <Label>Subject</Label>
                <Input
                  name="subject"
                  placeholder="What is your question about?"
                  className={`bg-white/50 border-white/30 backdrop-blur-sm ${
                    fieldErrors.subject ? "border-red-500" : ""
                  }`}
                  onChange={handleFieldChange}
                  required
                />
                {fieldErrors.subject && (
                  <p className="text-red-500 text-xs mt-1">
                    {fieldErrors.subject}
                  </p>
                )}
              </div>

              <div>
                <Label>Message</Label>
                <Textarea
                  name="message"
                  placeholder="Describe your issue or question here"
                  rows={4}
                  className={`bg-white/50 border-white/30 backdrop-blur-sm resize-none ${
                    fieldErrors.message ? "border-red-500" : ""
                  }`}
                  onChange={handleFieldChange}
                  required
                />
                {fieldErrors.message && (
                  <p className="text-red-500 text-xs mt-1">
                    {fieldErrors.message}
                  </p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full primaryButton py-3"
                disabled={isLoading}
              >
                {isLoading ? "Submitting..." : "Submit"}
              </Button>
            </form>
          </div>
        </div>
      </Card>
    </div>
  );
};

type LabelProps = React.FC<{
  children: React.ReactNode;
}>;

const Label: LabelProps = ({ children }) => {
  return (
    <label className="block text-sm font-bold text-gray-900  mb-1">
      {children}
    </label>
  );
};

export default Support;
