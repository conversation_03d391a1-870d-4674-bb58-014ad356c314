import { AuthLayout } from "@/components/auth-layout";
import Link from "next/link";

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{ error: string }>;
}) {
  const params = await searchParams;

  return (
    <AuthLayout
      title="Something went wrong"
      description="We encountered an error while processing your request"
    >
      <div className="space-y-6">
        <div className="bg-red-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-red-800 mb-2">
            Error Details
          </h3>
          {params?.error ? (
            <p className="text-sm text-red-700">Code error: {params.error}</p>
          ) : (
            <p className="text-sm text-red-700">
              An unspecified error occurred.
            </p>
          )}
        </div>

        <div className="text-center">
          <Link
            href="/auth/login"
            className="text-blue-600 hover:text-blue-500 font-medium"
          >
            Back to Login
          </Link>
        </div>
      </div>
    </AuthLayout>
  );
}
