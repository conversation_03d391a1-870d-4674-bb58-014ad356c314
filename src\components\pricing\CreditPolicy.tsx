import React from "react";
import { Card, CardContent } from "../ui/card";
import { creditResetPolicy } from "@/data/pricing-plans";

const CreditPolicy = () => {
  return (
    <Card className="glassColor rounded-sm">
      <CardContent className="p-2 py-0">
        <h3 className="font-extrabold text-center mb-1">Credit Reset Policy</h3>
        <div className="text-sm text-gray-600 space-y-1">
          <TitleDescription
            title="Free Plan"
            description={creditResetPolicy.freePlan}
          />
          <TitleDescription
            title="Paid Plans"
            description={creditResetPolicy.paidPlans}
          />
        </div>
      </CardContent>
    </Card>
  );
};

type TitleDescriptionProps = React.FC<{
  title: string;
  description: string;
}>;
const TitleDescription: TitleDescriptionProps = ({ description, title }) => {
  return (
    <p className="text-center" style={{ color: "#555555" }}>
      <span style={{ color: "#9A6600" }} className="font-bold">
        {title}:&nbsp;
      </span>
      {description}
    </p>
  );
};

export default CreditPolicy;
