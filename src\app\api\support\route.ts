import { createClient } from "@/lib/server";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const supabase = await createClient();

    // Get the current user
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const data = {
      name: formData.get("name") as string,
      email: formData.get("email") as string,
      subject: formData.get("subject") as string,
      message: formData.get("message") as string,
    };

    // Basic server-side validation (frontend handles detailed validation)
    if (!data.name?.trim() || !data.subject?.trim() || !data.message?.trim()) {
      return NextResponse.json(
        { error: "All fields are required" },
        { status: 400 }
      );
    }

    // Ensure the email matches the authenticated user's email
    if (data.email !== user.email) {
      return NextResponse.json(
        { error: "Email must match your account email" },
        { status: 400 }
      );
    }

    // Insert the support request into the database
    const { error: insertError } = await supabase.from("support").insert({
      name: data.name,
      email: data.email,
      subject: data.subject,
      message: data.message,
      userId: user.id,
    });

    if (insertError) {
      console.error("Support insertion error:", insertError);
      return NextResponse.json(
        { error: "Failed to submit support request" },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        success: true,
        message: "Support request submitted successfully",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Support API error:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
