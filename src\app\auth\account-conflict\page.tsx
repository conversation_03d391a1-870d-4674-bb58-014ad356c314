import { AuthLayout } from "@/components/auth-layout";
import { AccountLinkForm } from "@/components/account-link-form";
import Link from "next/link";

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{ email?: string }>;
}) {
  const params = await searchParams;
  const email = params?.email;

  if (!email) {
    return (
      <AuthLayout title="Invalid Request" description="Missing email parameter">
        <div className="space-y-6">
          <div className="bg-red-50 p-6 rounded-lg text-center">
            <h3 className="text-lg font-semibold text-red-800 mb-2">
              Invalid Request
            </h3>
            <p className="text-sm text-red-700">
              This page requires an email parameter. Please try signing in
              again.
            </p>
          </div>

          <div className="text-center">
            <Link
              href="/auth/login"
              className="text-blue-600 hover:text-blue-500 font-medium"
            >
              Back to Login
            </Link>
          </div>
        </div>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout
      title="Link Your Accounts"
      description="Verify your password to link your Google account"
    >
      <div className="space-y-6">
        <div className="bg-blue-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">
            Account Linking Required
          </h3>
          <p className="text-sm text-blue-700 mb-4">
            You tried to sign in with Google using{" "}
            <span className="font-medium">{email}</span>, but an account with
            this email already exists using email and password authentication.
          </p>
          <p className="text-sm text-blue-700">
            To link your Google account, please verify your password below.
          </p>
        </div>

        <AccountLinkForm email={email} />

        <div className="relative my-4">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">Or</span>
          </div>
        </div>

        <div className="space-y-4">
          <Link
            href="/auth/login"
            className="w-full inline-flex items-center justify-center rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
          >
            Sign in with Email & Password Instead
          </Link>

          <Link
            href="/auth/forgot-password"
            className="w-full inline-flex items-center justify-center rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
          >
            Reset Password
          </Link>
        </div>
      </div>
    </AuthLayout>
  );
}
