import { LoginForm } from "@/components/login-form";
import { AuthLayout } from "@/components/auth-layout";
import { Suspense } from "react";
import { Skeleton } from "@/components/ui/skeleton";

function LoginFormSkeleton() {
  return (
    <div className="space-y-6">
      {/* Form fields */}
      <div className="space-y-4">
        {/* Email field */}
        <div className="space-y-2">
          <Skeleton className="h-4 w-12" /> {/* Label */}
          <Skeleton className="h-9 w-full" /> {/* Input */}
        </div>
        {/* Password field */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-16" /> {/* Password label */}
            <Skeleton className="h-4 w-28" /> {/* Forgot password link */}
          </div>
          <Skeleton className="h-9 w-full" /> {/* Input */}
        </div>
      </div>

      {/* Login button */}
      <Skeleton className="h-9 w-full" />

      {/* Sign up link */}
      <div className="text-center">
        <Skeleton className="h-4 w-48 mx-auto" />
      </div>

      {/* Separator */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <Skeleton className="h-px w-full" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <Skeleton className="h-4 w-16" />
        </div>
      </div>

      {/* Google button */}
      <Skeleton className="h-9 w-full" />
    </div>
  );
}

export default function Page() {
  return (
    <AuthLayout
      title="Welcome back"
      description="Enter your email below to login to your account"
    >
      <Suspense fallback={<LoginFormSkeleton />}>
        <LoginForm />
      </Suspense>
    </AuthLayout>
  );
}
