import { createClient } from "@/lib/server";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;

    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // First, verify the password for the existing email/password account
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (authError || !authData.user) {
      return NextResponse.json(
        { error: "Invalid email or password" },
        { status: 401 }
      );
    }

    // Get the current OAuth user (should be in session from the OAuth flow)
    const {
      data: { user: oauthUser },
    } = await supabase.auth.getUser();

    if (!oauthUser) {
      return NextResponse.json(
        { error: "No OAuth session found. Please try signing in with Google again." },
        { status: 400 }
      );
    }

    // Check if the OAuth user is different from the email/password user
    if (oauthUser.id === authData.user.id) {
      // Same user, just redirect to dashboard
      return NextResponse.json({
        success: true,
        message: "Account verified successfully",
        redirectTo: "/dashboard",
      });
    }

    // Different users - we need to merge them
    // For now, we'll keep the email/password account and delete the OAuth user
    // In a more complex scenario, you might want to merge data

    try {
      // Delete the OAuth user from auth.users (this will cascade to our users table if set up properly)
      const { error: deleteError } = await supabase.auth.admin.deleteUser(oauthUser.id);
      
      if (deleteError) {
        console.error("Error deleting OAuth user:", deleteError);
        // Continue anyway, as the main account is still valid
      }

      // Sign out the OAuth user and sign in the email/password user
      await supabase.auth.signOut();
      
      // Sign in with the verified email/password account
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (signInError) {
        return NextResponse.json(
          { error: "Failed to sign in after account linking" },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: "Accounts linked successfully",
        redirectTo: "/dashboard",
      });

    } catch (error) {
      console.error("Error during account linking:", error);
      return NextResponse.json(
        { error: "Failed to link accounts" },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error("Account linking error:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
