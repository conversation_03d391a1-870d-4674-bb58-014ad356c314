import { AuthLayout } from "@/components/auth-layout";
import Link from "next/link";

export default function Page() {
  return (
    <AuthLayout
      title=""
      description=""
      // title="Thank you for signing up!"
      // description="Check your email to confirm your account"
    >
      <div className="space-y-6">
        <div className="bg-green-50 p-6 rounded-lg text-center">
          <h3 className="text-lg font-semibold text-green-800 mb-2">
            Account Created Successfully
          </h3>
          <p className="text-sm text-green-700">
            You&apos;ve successfully signed up. Please check your email to
            confirm your account before signing in.
          </p>
        </div>

        <div className="text-center">
          <Link
            href="/auth/login"
            className="text-blue-600 hover:text-blue-500 font-medium"
          >
            Continue to Login
          </Link>
        </div>
      </div>
    </AuthLayout>
  );
}
