"use client";

/* eslint-disable react/no-unescaped-entities */
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { PricingPlan } from "@/data/pricing-plans";
import { cn } from "@/lib/utils";
import FeaturesList from "./FeaturesList";

interface PricingCardProps {
  plan: PricingPlan;
  onUpgrade?: (planId: string) => void;
  isCurrentPlan?: boolean;
}

export const PricingCard = ({
  plan,
  onUpgrade,
  isCurrentPlan = false,
}: PricingCardProps) => {
  const handleButtonClick = () => {
    if (onUpgrade && !plan.isCurrentPlan) {
      onUpgrade(plan.id);
    }
  };

  const isPremium = plan.id === "premium";
  const textColorClass = isPremium ? "text-white" : "text-[#028482]";

  return (
    <div className="w-full p-2 lg:w-1/2 xl:w-1/3">
      <div
        className={cn(
          "w-full rounded-sm h-full",
          isCurrentPlan
            ? "bg-gradient-to-r from-green-400/80 to-teal-600/80 p-1"
            : "bg-white"
        )}
      >
        <Card
          className={cn(
            "rounded-sm w-full h-full",
            isPremium ? "bg-[#028482]" : "bg-white"
          )}
        >
          <div className="p-5 border-b border-[#D3D3D3]">
            <div className="flex flex-row items-center justify-start gap-2">
              <h3 className={cn("text-md font-bold", textColorClass)}>
                {plan.name}
              </h3>
              {isCurrentPlan && (
                <Badge
                  className={cn(
                    "text-xs rounded-full",
                    isPremium
                      ? "bg-white text-[#028482]"
                      : "bg-[#028482] text-white"
                  )}
                >
                  Current Plan
                </Badge>
              )}
            </div>
            <div className={cn("my-4", textColorClass)}>
              <div className="text-3xl font-bold">
                {plan.currency}
                {plan.price}
              </div>
              <div className={`text-sm font-bold`}>{plan.period}</div>
            </div>

            <Button
              className={cn(
                "w-full cursor-pointer",
                isPremium
                  ? "bg-white text-[#028482] hover:bg-white/50"
                  : "bg-[#028482] text-white hover:bg-[#028482]/50"
              )}
              disabled={isCurrentPlan}
              onClick={handleButtonClick}
            >
              Upgrade
            </Button>
          </div>

          <div className={cn("px-5", textColorClass)}>
            <h4 className="font-bold mb-3">What's included</h4>
            <FeaturesList features={plan.features} isPremium={isPremium} />
          </div>
        </Card>
      </div>
    </div>
  );
};
