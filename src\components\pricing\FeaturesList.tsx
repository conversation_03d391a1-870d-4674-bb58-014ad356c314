import { PricingFeature } from "@/data/pricing-plans";
import { cn } from "@/lib/utils";
import React from "react";

type FeaturesListProps = React.FC<{
  isPremium: boolean;
  showAll?: boolean;
  features: PricingFeature[];
}>;
const FeaturesList: FeaturesListProps = ({
  features,
  isPremium = false,
  showAll = true,
}) => {
  return (
    <div className="space-y-2 text-sm">
      {features
        .slice(0, showAll ? features.length : 3)
        .map((feature, index) => (
          <div key={index} className="flex items-center gap-2">
            <span>
              {feature.included ? <Check isPremium={isPremium} /> : <Wrong />}
            </span>
            <span className={cn(feature.included ? "" : "text-gray-400")}>
              {feature.text}
            </span>
          </div>
        ))}
    </div>
  );
};

const Check = ({ isPremium = false }) => {
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_213_3170)">
        <circle
          cx="8.5"
          cy="8.5"
          r="8.5"
          fill={!isPremium ? "#D2FFEC" : "#028482"}
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M0 8.5C0 6.24566 0.895533 4.08365 2.48959 2.48959C4.08365 0.895533 6.24566 0 8.5 0C10.7543 0 12.9163 0.895533 14.5104 2.48959C16.1045 4.08365 17 6.24566 17 8.5C17 10.7543 16.1045 12.9163 14.5104 14.5104C12.9163 16.1045 10.7543 17 8.5 17C6.24566 17 4.08365 16.1045 2.48959 14.5104C0.895533 12.9163 0 10.7543 0 8.5ZM8.01493 12.138L12.9087 6.02027L12.0247 5.31307L7.85173 10.5275L4.896 8.0648L4.17067 8.9352L8.01493 12.138Z"
          fill={isPremium ? "#D2FFEC" : "#028482"}
        />
      </g>
      <defs>
        <clipPath id="clip0_213_3170">
          <rect width="17" height="17" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

const Wrong = () => {
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_213_3185)">
        <circle cx="8.5" cy="8.5" r="8.5" fill="#FFE6E6" />
        <path
          d="M5.44 12.75L8.5 9.69L11.56 12.75L12.75 11.56L9.68999 8.5L12.75 5.44L11.56 4.25L8.5 7.31L5.44 4.25L4.25 5.44L7.31 8.5L4.25 11.56L5.44 12.75ZM8.5 17C7.32416 17 6.21916 16.7767 5.185 16.3302C4.15083 15.8837 3.25125 15.2782 2.48625 14.5137C1.72125 13.7493 1.11577 12.8497 0.669801 11.815C0.223834 10.7803 0.000567742 9.67526 1.07595e-06 8.5C-0.00056559 7.32473 0.222701 6.21973 0.669801 5.185C1.1169 4.15027 1.72238 3.25068 2.48625 2.48625C3.25012 1.72182 4.1497 1.11633 5.185 0.6698C6.2203 0.223267 7.3253 0 8.5 0C9.67469 0 10.7797 0.223267 11.815 0.6698C12.8503 1.11633 13.7499 1.72182 14.5137 2.48625C15.2776 3.25068 15.8834 4.15027 16.331 5.185C16.7787 6.21973 17.0017 7.32473 17 8.5C16.9983 9.67526 16.775 10.7803 16.3302 11.815C15.8854 12.8497 15.2799 13.7493 14.5137 14.5137C13.7476 15.2782 12.848 15.8839 11.815 16.331C10.782 16.7781 9.67696 17.0011 8.5 17Z"
          fill="#FF3831"
        />
      </g>
      <defs>
        <clipPath id="clip0_213_3185">
          <rect width="17" height="17" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default FeaturesList;
