import { ReactNode } from "react";

interface AuthLayoutProps {
  children: ReactNode;
  title: string;
  description: string;
}

export function AuthLayout({ children, title, description }: AuthLayoutProps) {
  return (
    <div className="min-h-screen flex">
      {/* Left side - Gradient background */}
      <div
        className="hidden lg:flex lg:w-1/2 items-center justify-center p-12"
        style={{
          background:
            "linear-gradient(107.94deg, rgba(3, 243, 144, 0.8) -6.38%, rgba(2, 132, 130, 0.8) 111.04%)",
        }}
      >
        <div className="max-w-md text-white">
          <h1 className="text-4xl font-bold mb-6">Welcome to Pointify</h1>
          <p className="text-lg opacity-90">
            Transform your business with our powerful platform. Join thousands
            of users who trust us with their success.
          </p>
        </div>
      </div>

      {/* Right side - Form area */}
      <div className="w-full lg:w-1/2 bg-white flex items-center justify-center p-6 md:p-12">
        <div className="w-full max-w-md">
          <div className="mb-8">
            <h2
              style={{ color: "rgba(3, 243, 144, 0.8)" }}
              className="text-3xl font-bold text-gray-900 mb-2"
            >
              {title}
            </h2>
            <p className="text-gray-600">{description}</p>
          </div>
          {children}
        </div>
      </div>
    </div>
  );
}
