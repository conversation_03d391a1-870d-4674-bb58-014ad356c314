/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Maybe } from "@/types/common";
import { UsersType } from "@/types/db";
import { useContext, createContext } from "react";

const UserContext = createContext<Maybe<UsersType>>(null);

type AppProviderProps = {
  userData: UsersType | null;
  children: React.ReactNode;
};

export function AppProvider({ userData, children }: AppProviderProps) {
  return (
    <UserContext.Provider value={userData as any}>
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  return useContext(UserContext);
}
