"use client";

import { useTheme } from "next-themes";
import { Toaster as Son<PERSON>, ToasterProps } from "sonner";

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme();

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast: "glassColor border-white/60 text-gray-900 shadow-lg",
          description: "text-gray-700",
          actionButton: "primaryButton text-white",
          cancelButton: "bg-white/20 text-gray-900 border-white/30",
        },
      }}
      style={
        {
          "--normal-bg": "rgba(255, 255, 255, 0.5)",
          "--normal-text": "rgb(17, 24, 39)",
          "--normal-border": "rgba(255, 255, 255, 0.6)",
        } as React.CSSProperties
      }
      {...props}
    />
  );
};

export { Toaster };
