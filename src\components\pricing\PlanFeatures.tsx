import React, { useMemo, useState } from "react";
import { UsersType } from "@/types/db";
import { Maybe } from "@/types/common";
import { pricingPlans } from "@/data/pricing-plans";
import FeaturesList from "./FeaturesList";

type PlanFeaturesProps = React.FC<{
  userData: Maybe<UsersType>;
}>;
const PlanFeatures: PlanFeaturesProps = ({ userData }) => {
  const [showMore, setShowMore] = useState(false);

  const features = useMemo(() => {
    if (userData) {
      const planName = userData.plan?.name;
      if (planName) {
        return pricingPlans.find((f) => f.name === planName)?.features ?? [];
      }
      return [];
    }
    return [];
  }, [userData]);

  return (
    <div className="flex flex-col w-full">
      <div className="flex w-full items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-600">Plan Features</h3>
        <span
          className="text-teal-600 text-xs flex cursor-pointer"
          onClick={() => {
            setShowMore(!showMore);
          }}
        >
          View more details
          <MoreLess showMore={showMore} />
        </span>
      </div>
      <FeaturesList features={features} showAll={showMore} isPremium={false} />
    </div>
  );
};

type MoreLessProps = React.FC<{
  showMore: boolean;
}>;
const MoreLess: MoreLessProps = ({ showMore }) => {
  return (
    <svg
      width="16"
      height="17"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`transition-transform duration-300 ${
        showMore ? "rotate-180" : "rotate-0"
      }`}
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8.00048 10.7075L11.8545 6.85448L11.1475 6.14648L8.00048 9.29348L4.85448 6.14648L4.14648 6.85448L8.00048 10.7075Z"
        fill="#028482"
      />
    </svg>
  );
};

export default PlanFeatures;
